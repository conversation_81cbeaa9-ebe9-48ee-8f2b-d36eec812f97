/* eslint-disable no-nested-ternary */
import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Button, Text, Modal, Collapse, Icon, message, StatusTip, PopConfirm } from '@tencent/tea-component';
import { archInfo, nodeList, nodeIcon, appId, unSupportedProducts, updateGuardInfo } from '@src/utils/caching';
import moment from 'moment';
// import EscortTool from '@src/pages/escortTool';
import { t } from '@tea/app/i18n';
import { Limit, getStatu, processState } from '@src/utils/common';
import { setGuardUpdateFlagAction, setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
import {
  DescribeOtherPlatformGuardSheet,
  DescribeProductConfigList,
  DescribeGuardProductPolicy,
  BindArchGuardInstance,
  CreateGuardSheet,
  DescribeArchGuardInstance,
  ModifyGuardInstances,
} from '@src/service/api/baseInfo';
import './index.less';
import _ from 'lodash';
import Loading from '@src/components/Loading';
import ResourceConfig from './components/ResourceConfig';
import { nanoid } from 'nanoid';
import EscortDrawer from '@src/pages/escortDrawer/index';
import store from '@src/origin-store/store';

function arrayContainObject(obj, list) {
  // eslint-disable-next-line @typescript-eslint/prefer-for-of
  for (let i = 0; i < list.length; i++) {
    if (_.isEqual(list[i], obj)) {
      return true;
    }
  }
  return false;
}

interface IProps {
  canEdit?: boolean;
  consoleBaseInfo?: boolean;
  statusChange?: (status: boolean) => void;
  // 历史护航单信息
  historyGuardInfo?: any
}

// 护航资源列表
const ResourceList = ({ consoleBaseInfo = false, canEdit = false, statusChange = () => {} }: IProps, ref) => {
  // 历史护航信息
  const { historyGuard, drawerUpdateFlag } = store.getState().guard;
  // 是否是运营端
  const isISA = archInfo.env === 'ISA';
  // 实例总数量，用来分页查询已保存的全部实例
  const instanceTotalNumber = nodeList?.reduce((sum, item) => sum + item.GuardCount, 0);
  // 保存的全部实例
  const [allInstance, setAllInstance] = useState([]);
  // 展开的节点
  const [activeIds, setActiveIds] = useState([]);
  // 已经展开过的节点
  const [requestedIds, setRequestedIds] = useState([]);
  // 全部搜索条件
  const [allFilter, setAllFilter] = useState([]);
  // 全部展示列
  const [allColumn, setAllColumn] = useState([]);
  // 所有的实例policy
  const [allInstancePolicy, setAllInstancePolicy] = useState([]);
  // 所有的产品policy
  const [allProductPolicy, setAllProductPolicy] = useState([]);
  // 加入护航的实例，也就是待保存的实例，按照节点分组
  const [joinGuardInstance, setJoinGuardInstance] = useState({});
  // 产品报备信息
  const [productTemplate, setProductTemplate] = useState({});
  // 产品报备信息
  const [productRemark, setProductRemark] = useState({});
  // 未选择实例
  const [noInstance, setNoInstance] = useState([]);
  // 有必填项未填完的节点
  const [noFinishInstance, setNoFinishInstance] = useState([]);
  // 未填完产品报备的节点
  const [noFinishProduct, setNoFinishProduct] = useState([]);
  const [guardInfo, setGuardInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [saveVisible, setSaveVisible] = useState(false);
  const [submitVisible, setSubmitVisible] = useState(false);
  // 不可以提交的判断条件
  const [noSave, setNoSave] = useState(true);
  // 原来护航实例dict
  const [sourceInstanceTemplateDict, setSourceInstanceTemplateDict] = useState({});

  // 查询护航单
  function getGuard() {
    // 查询架构图对应的护航单信息
    DescribeOtherPlatformGuardSheet({ Filters: [{ Name: 'platform_unique_id', Values: [archInfo.archInfo?.archId] }] })
      .then((res: any) => {
        // 最后一条护航单
        const guard = res.Guard?.length ? res.Guard[0] : {};
        // 如果最后一条已经护航完成，则不缓存
        setGuardInfo(getStatu(guard) === 'finish' ? {} : _.cloneDeep(guard));
      })
      .catch(() => {
        setDataLoading(false);
      });
  }
  // 获取产品配置
  function getDescribeProductConfigList() {
    DescribeProductConfigList({ Env: 'all', TaskType: 'guardTaskType' })
      .then((res: any) => {
        setAllColumn(res.FilterPolicy || []);
        setAllFilter(res.SearchFilterPolicy || []);
      })
      .catch((err) => {
        console.log(err);
      });
  }

  // 获取policy
  function getPolicy() {
    DescribeGuardProductPolicy().then((res: any) => {
      setAllInstancePolicy(res.InstancePolicy || []);
      setAllProductPolicy(res.ProductPolicy || []);
    })
      .catch((err) => {
        console.log(err);
      });
  }

  // 查询保存的实例
  function getSavedPageInstance(pageSize = 1) {
    return new Promise((resolve, reject) => {
      DescribeArchGuardInstance({
        GuardId: guardInfo.GuardId,
        Offset: (pageSize - 1) * Limit,
        Limit,
      })
        .then((res: any) => {
          resolve(res.Instance || []);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  // 分页查询已经保存的实例
  function getSavedAllInstance() {
    setDataLoading(true);
    // 实例的总页数
    const pages = Math.ceil(instanceTotalNumber / Limit);
    const resultList = [];
    for (let i = 1; i <= pages; i++) {
      resultList.push(getSavedPageInstance(i));
    }
    Promise.all(resultList).then((res) => {
      if (res?.length) {
        // 是否有某次查询没有返回值
        const hasErr = res.some(i => i.length === 0);
        if (hasErr) {
          message.error({ content: t('返回数据有误，请刷新页面') });
          setDataLoading(false);
          return;
        }
        const instances = res.flat() || [];
        const savedInstance = instances.map((i) => {
          i.JoinGuard = 1;
          return i;
        });
        setAllInstance(_.cloneDeep(savedInstance));
        setDataLoading(false);
        // 获取产品对应实例
        const tmpInstDict = {};
        savedInstance.map((i) => {
          if (tmpInstDict[i.Product]) {
            tmpInstDict[i.Product].push(i);;
          } else {
            tmpInstDict[i.Product] = [i];
          }
        });
        setSourceInstanceTemplateDict(tmpInstDict);
      } else {
        setDataLoading(false);
      }
    })
      .finally(() => {
        setDataLoading(false);
      });
  }

  // 监听下层组件实例变化，按照 NodeUuid 分组保存
  function instanceChange(nodeUuid, instanceList) {
    setJoinGuardInstance((obj) => {
      obj[nodeUuid] = instanceList;
      return _.cloneDeep(obj);
    });
  }

  // 监听下层组件产品报备信息变化
  function productPolicyChange(node, productData) {
    setProductTemplate((obj) => {
      const template = {
        AppId: Number(appId),
        NodeUuid: node.NodeUuid,
        Product: node.Product,
        Policy: productData,
      };
      obj[node.NodeUuid] = [template];
      return _.cloneDeep(obj);
    });
  }

  // 监听下层组件产品备注变化，按照 NodeUuid 分组保存
  function remarkChange(node, remark) {
    setProductRemark((obj) => {
      const newRemark = {
        ...remark,
        AppId: Number(appId),
        NodeUuid: node.NodeUuid,
        Product: node.Product,
        Comment: remark.Comment,
      };
      obj[node.NodeUuid] = [newRemark];
      return _.cloneDeep(obj);
    });
  }

  // 分页保存所有实例的函数
  function savePageInstances(Timestamp, TemplateId, InstanceTemplate) {
    return new Promise((resolve, reject) => {
      BindArchGuardInstance({
        MapId: archInfo?.archInfo?.archId,
        GuardId: guardInfo.GuardId,
        // 租户端和运营端传的字段名不同
        [isISA ? 'Timestamp' : 'TimestampForBind']: Timestamp,
        TemplateId,
        InstanceTemplate,
      }).then((res) => {
        resolve(res);
      })
        .catch((err) => {
          reject(err);
        });
    });
  }

  // 获取修改了产品粒度策略的产品
  function getModifiedPolicyProducts(cpTemplate) {
    const tmp = [];
    cpTemplate.map((i) => {
      const item = {
        AppId: i.AppId,
        Product: i.Product,
      };
      tmp.push(item);
    });
    return tmp;
  }

  // 获取修改了实例的产品
  function getModifiedProducts(newInstance) {
    const newInstanceDict = {};
    newInstance.forEach((i) => {
      if (newInstanceDict[i.Product]) {
        newInstanceDict[i.Product].push(i);
      } else {
        newInstanceDict[i.Product] = [i];
      }
    });
    const tmp = [];
    // eslint-disable-next-line no-restricted-syntax
    for (const key in newInstanceDict) {
      const src = (sourceInstanceTemplateDict[key] || []).sort();
      const target = (newInstanceDict[key] || []).sort();
      const obj = {
        AppId: Number(appId),
        Product: key,
      };
      if (!(_.isEqual(src, target)) && !arrayContainObject(obj, tmp)) {
        tmp.push(obj);
      }
    }
    return tmp || [];
  }

  // 修改护航单实例
  const modifyGuard = async (TemplateId, ProductDesc, ProductTemplate, ModifiedProduct) => {
    const modifiedPolicyProducts = getModifiedPolicyProducts(ProductTemplate);
    const params = {
      TemplateId,
      GuardId: guardInfo.GuardId,
      AppId: guardInfo.MainAppId,
      // 修改了选择实例的产品
      ModifiedProduct,
      // 修改了产品策略的产品
      ModifiedPolicyProduct: modifiedPolicyProducts,
      ProductDesc,
      ProductTemplate,
      Operator: archInfo?.userName,
    };
    ModifyGuardInstances(params)
      .then(res => Promise.resolve(res))
      .catch(err => Promise.reject(err));
  };

  // 节点设置保存
  function saveNode(check = true, update = false, showTip = true) {
    return new Promise((resolve, reject) => {
      // 将所有节点的实例合并到一起
      let allInstance = [].concat(...Object.values(joinGuardInstance));
      if (!allInstance.length) {
        message.error({ content: t('护航资源为0，无法提交流程，请确认') });
        return;
      }
      // 保存实例包含的全部产品
      const savedProduct = Array.from(new Set(allInstance.map(i => i.Product)));
      // 只有点击提交时就校验，如果未选实例或未填写完全
      if (check && noSave) {
        return;
      }
      setLoading(true);
      // 兜底产品报备信息
      const unSupportedDesc = [...(guardInfo.ProductDesc || [])
        .filter(i => unSupportedProducts.includes(i.Product))];
      // 非兜底产品报备信息，需要去掉没有选择实例的产品
      const supportedDesc = (Object.values(productRemark) || [])
        .flat()
        .filter((i: any) => savedProduct.includes(i.Product));
      // 将所有节点的产品报备信息合并到
      const allProductTemplate = [].concat(...Object.values(productTemplate));
      // 所有实例按照字典序排序
      allInstance.sort((obj1, obj2) => obj1.InstanceId.localeCompare(obj2.InstanceId));
      // 如果是租户端，要去掉AppID和JoinGuard
      if (!isISA) {
        allInstance = allInstance.map((i) => {
          delete i.AppId;
          delete i.JoinGuard;
          delete i.Status;
          delete i.Tag;
          return i;
        });
      }
      // 总页数
      const pages = Math.ceil(allInstance.length / Limit);
      // 当前时间戳
      const Timestamp = moment().valueOf();
      const TemplateId = nanoid();
      const resultList = [];
      for (let i = 1; i <= pages; i++) {
        // 实例起止下标
        const startIndex = (i - 1) * Limit;
        const endIndex = ((i - 1) * Limit) + Limit;
        const data = allInstance.slice(startIndex, endIndex);
        resultList.push(savePageInstances(Timestamp, TemplateId, data));
      }
      Promise.all([...resultList]).then((res) => {
        if (res?.length) {
          // 是否有某次查询没有返回值
          const hasErr = res.some(i => i.length === 0);
          if (hasErr) {
            message.error({ content: t('当前绑定实例过多，请重试，或者减少绑定的实例数量') });
            setLoading(false);
            return;
          }
          if (update) {
            const ModifiedProduct = getModifiedProducts(allInstance);
            // 护航中实例变更后的逻辑
            modifyGuard(TemplateId, [...supportedDesc, ...unSupportedDesc], allProductTemplate, ModifiedProduct)
              .then(() => {
                message.success({ content: t('护航资源修改成功！') });
                store.dispatch(setGuardUpdateFlagAction(!drawerUpdateFlag));
                archInfo.closeDrawer();
              })
              .catch((err) => {
                console.log(err);
              })
              .finally(() => {
                setLoading(false);
              });
          } else {
            // 未提单前的逻辑
            let params = {
              ...guardInfo,
              TemplateId,
              ProductTemplate: allProductTemplate,
              ProductDesc: [...supportedDesc, ...unSupportedDesc],
            };
            // 适配租户端传参
            if (!isISA) {
              const PlatformInfo = (guardInfo.CloudGuardBaseInfoOtherPlatform || [])
                .find(i => i.PlatformUniqueId === archInfo.archInfo?.archId) || {};
              params = {
                GuardId: guardInfo.GuardId,
                Status: guardInfo.Status || 1,
                Platform: PlatformInfo.Platform || archInfo.env,
                PlatformUniqueId: PlatformInfo.PlatformUniqueId || archInfo.archInfo?.archId,
                GuardName: guardInfo.GuardName,
                StartTime: guardInfo.StartTime,
                EndTime: guardInfo.EndTime,
                TemplateId,
              };
            }
            // 调用createOrModifyGuard保存
            CreateGuardSheet(params)
              .then(() => {
                if (showTip) {
                  updateGuardInfo();
                  if (check) {
                    setSubmitVisible(true);
                  } else {
                    setSaveVisible(true);
                  }
                  store.dispatch(setGuardUpdateFlagAction(!drawerUpdateFlag));
                  // archInfo.setSlotComponent(<EscortTool uuid={nanoid()} />);
                }
                resolve(true);
              })
              .catch(err => reject(err))
              .finally(() => {
                setLoading(false);
              });
          }
        } else {
          setLoading(false);
        }
      })
        .catch((err) => {
          console.log(err);
          message.error({ content: t('当前绑定实例过多，请重试，或者减少绑定的实例数量') });
          setLoading(false);
        });
    });
  }

  // 监听实例、产品信息变化
  useEffect(() => {
    // 实例报备信息未完善或者产品信息未完善，不可以提交
    const flag = noFinishInstance.length > 0
    || (noFinishProduct.length > 0 && noFinishProduct.some(value => !noInstance.includes(value)));
    setNoSave(flag);
    // 将是否可以下一步抛到上层
    statusChange?.(!noSave);
  }, [noInstance, noFinishInstance, noFinishProduct]);

  // 实例报备信息校验
  useEffect(() => {
    const noList = [];
    const tempList = [];
    // 选择了实例的节点
    const keys = Object.keys(joinGuardInstance);
    keys.forEach((key) => {
      // 选择的实例
      const instanceList = joinGuardInstance[key];
      if (!instanceList.length) {
        // 记录未选择实例的节点
        noList.push(key);
      } else {
        // 判断是否有未填写的必填项
        const noFinish = instanceList.some(obj =>
          // 针对开关类型的policy，开关类型的policy的FieldType不能为float
          (obj.Policy || []).some(policy => (policy.IsRequired || policy.FieldType === 'float') && (policy.Type === 'Other' ? !policy.OtherValue : !policy.Value)));
        if (noFinish) {
          tempList.push(key);
        }
      }
    });
    setNoInstance(_.cloneDeep(noList));
    setNoFinishInstance(_.cloneDeep(tempList));
  }, [joinGuardInstance]);

  // 产品报备信息校验
  useEffect(() => {
    const tempList = [];
    // 自助护航不校验产品报备信息
    if (guardInfo.GuardId && guardInfo.Standard !== 3) {
      // 填了报备信息的节点
      const keys = Object.keys(productTemplate);
      keys.map((key) => {
        const templateList = productTemplate[key] || [];
        // 判断是否有未填写的必填项
        const noFinish = templateList.some(obj => obj.Policy.some(policy => policy.IsRequired && !policy.Value));
        // 如果有未填写的，并且是选择实例的节点
        if (noFinish && !noInstance.includes(key)) {
          tempList.push(key);
        }
      });
    }
    setNoFinishProduct(_.cloneDeep(tempList));
  }, [productTemplate]);

  useEffect(() => {
    // 记录已经展开过的节点
    setRequestedIds(Array.from(new Set([...requestedIds, ...activeIds])));
  }, [activeIds]);

  useEffect(() => {
    // 如果传入护航信息，就展示历史护航，不然就展示最新的
    if (historyGuard?.GuardId) {
      setGuardInfo({ ...historyGuard });
    } else {
      getGuard();
    }
  }, [historyGuard]);

  useEffect(() => {
    if (guardInfo.GuardId) {
      getSavedAllInstance();
    } else {
      setDataLoading(false);
    }
  }, [guardInfo]);

  useEffect(() => {
    setDataLoading(true);
    getDescribeProductConfigList();
    if (isISA) {
      getPolicy();
    }
  }, []);

  useImperativeHandle(ref, () => ({
    save: check => new Promise((resolve, reject) => {
      saveNode(check, false, false).then(() => resolve(true))
        .catch(() => reject(false));
    }),
  }));

  // 查询是否有修改护航单实例权限
  function isInstanceModifyAllowed() {
    let stateAllowed = true;
    let guysAllowed = true;
    // 护航期间，草稿状态和计算状态不允许修改
    if (!guardInfo.GuardInfoSupportUpdate || guardInfo.Status <= processState.onNoteId
              || [processState.onRunningId, processState.instanceAltering, processState.instanceAlteredRun]
                .includes(guardInfo.Status)) {
      stateAllowed = false;
    }
    // 护航负责人审批状态，允许修改。该状态较特殊
    if (processState.onRunningId === guardInfo.Status && !guardInfo.Approvals.AfterSalesStatus.IsConfirm) {
      stateAllowed = true;
    }
    // 有权限修改的人员
    const insEditorList = getInstanceEditor();
    if (insEditorList.indexOf(archInfo?.userName) === -1) {
      guysAllowed = false;
    }
    return stateAllowed && guysAllowed;
  }

  // 查询是有修改护航单实例的人员：建单人、改单人、APPID负责人、审批人及售后指派人
  function getInstanceEditor() {
    let guys = [];
    guys = guys.concat(guardInfo?.Approvals?.AfterSalesStatus?.Handler?.split(';'))
      .concat(guardInfo?.Approvals?.AfterSalesStatus?.Supporter?.split(';'));
    if (guardInfo?.Approvals?.ExpertStatus) {
      guardInfo.Approvals.ExpertStatus.map((i) => {
        guys = guys.concat(i.Handler.split(';'));
      });
    }
    if (guardInfo?.Approvals?.ScanResultStatus) {
      guardInfo.Approvals.ScanResultStatus.map((i) => {
        guys = guys.concat(i.Handler.split(';'));
      });
    }
    guys = guys.concat(guardInfo?.CreatedBy?.trim())
      .concat(guardInfo?.UpdatedBy?.trim())
      .concat(guardInfo?.Responser?.split(';'));
    return Array.from(new Set(guys)).filter(i => i !== '');
  }


  return <div className='resourceListWrap'>
    {
      dataLoading ? <StatusTip status='loading' />
        : <>
          <Collapse
            className='resourceList'
            icon={active => (active ? <Icon type="arrowdown" /> : <Icon type="arrowright" />)}
            activeIds={activeIds}
            onActive={(v) => {
              setActiveIds(v);
            }}
            destroyInactivePanel={false}
          >
            {(nodeList || []).map((node: any) => {
              // 该节点的全部搜索条件
              const productFilter = allFilter.filter(i => i.Product === node.Product)?.[0]?.FilterInfo || [];
              // 该节点的展示列
              let productColumn = allColumn.filter(i => i.Product === node.Product)?.[0]?.FilterInfo.filter(i => i.Uses === 'Right') || [];
              // 该节点的实例policy
              let instancePolicy = allInstancePolicy[node.Product] || [];
              // 该节点的产品policy
              let productPolicy = allProductPolicy[node.Product] || [];
              // 该节点的备注
              const remark = (guardInfo.ProductDesc || []).find(i => i.NodeUuid === node.NodeUuid) || {};

              // 租户端不需要实例和产品的policy
              if (!isISA) {
                productColumn = productColumn.filter(i => i.DataFrom !== 'Policy');
                instancePolicy = [];
                productPolicy = [];
              }
              // 如果节点未选择，则不展示产品报备信息
              if (noInstance.includes(node.NodeUuid)) {
                productPolicy = [];
              }

              return <Collapse.Panel
                id={node.NodeUuid}
                key={node.NodeUuid}
                title={<div className='resourceTitle'>
                  <div>{node.NodeName}</div>
                  <div className='resourceWrap'>
                    <div className='icon' dangerouslySetInnerHTML={{ __html: nodeIcon[node.ProductType] }}></div>
                    <div className='resourceNum'>
                      <Text theme="text" className='numType'>{t('总实例')}<span>{node.Count || 0}</span></Text>
                      <Text theme="success" className='numType'>{t('护航实例')}<span>{node.GuardCount || 0}</span></Text>
                      <Text theme="primary" className='numType'>{t('重点关注实例')}<span>{node.ImportantCount || 0}</span></Text>
                      {
                       (noFinishInstance.includes(node.NodeUuid)
                        || (noFinishProduct.includes(node.NodeUuid) && !noInstance.includes(node.NodeUuid)))
                         ? <Text theme="danger" className='numType'>{t('需完善报备信息')}</Text>
                         : ''
                      }
                    </div>
                  </div>
                </div>}>
                <ResourceConfig
                  remark={remark}
                  remarkChange={(remark) => {
                    remarkChange(node, remark);
                  }}
                  canEdit={canEdit}
                  consoleBaseInfo={consoleBaseInfo}
                  instanceChange={(instanceList) => {
                    instanceChange(node.NodeUuid, instanceList);
                  }}
                  productPolicyChange={(productData) => {
                    productPolicyChange(node, productData);
                  }}
                  savedInstance={allInstance.filter(i => i.NodeUuid === node.NodeUuid)}
                  requested={requestedIds.includes(node.NodeUuid)}
                  nodeInfo={node}
                  productFilter={productFilter}
                  productColumn={productColumn}
                  instancePolicy={instancePolicy}
                  productPolicy={productPolicy}
                />
              </Collapse.Panel>;
            })}

          </Collapse>
          {
            canEdit
            // 能编辑就展示按钮
              ? (guardInfo.Status > 1
                // 已提单
                ? <div className='btnWrap'>
                    <PopConfirm
                        disabled={noSave || !isInstanceModifyAllowed()}
                        arrowPointAtCenter={true}
                        title={t('确认要提交修改结果吗？')}
                        message={t('提交后，变更实例将发起新的【巡检】与【审批】。')}
                        footer={close => (
                          <>
                            <Button type={'primary'} onClick={() => {
                              saveNode(true, true);
                              close();
                            }}>
                              {t('确定')}
                            </Button>
                            <Button onClick={() => {
                              close();
                            }} type="text">{t('取消')}</Button>
                          </>
                        )}
                        placement={'top'}>
                        <Button type="primary" disabled={noSave || !isInstanceModifyAllowed()}>{t('提交')}</Button>
                    </PopConfirm>
                   </div>
                // 未护航
                : <div className='btnWrap'>
                    <Button type="weak" disabled={!noSave} className='saveBtn' onClick={() => {
                      saveNode(false);
                    }}>{t('保存')}</Button>
                    <Button type="primary" disabled={noSave} onClick={() => {
                      saveNode();
                    }}>{t('提交')}</Button>
                </div>)
              : ''
          }
        </>
    }

    {/* 全屏遮罩 */}
    <Loading show={loading}></Loading>
    {/* 保存成功后的弹框 */}
    <Modal visible={saveVisible} caption={t('已保存节点设置')} onClose={() => {
      setSaveVisible(false);
    }}>
      <Modal.Body>{t('已保存各节点配置信息，部分节点未配置报备信息，请继续补充完整。')}</Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={() => {
          setSaveVisible(false);
        }}>
          {t('确定')}
        </Button>
      </Modal.Footer>
    </Modal>
    <Modal visible={submitVisible} caption={t('已保存节点设置')} onClose={() => {
      setSubmitVisible(false);
    }}>
      <Modal.Body>{t('已保存各节点配置信息，可继续提交护航。')}</Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={() => {
          store.dispatch(setDrawerVisibleMapAction({}));
          setSubmitVisible(false);
          // 设置发起护航抽屉内容
          archInfo.setDrawerProps({
            title: t('发起护航'),
            children: <EscortDrawer />,
            className: 'escortDrawerWrap',
            extra: { outerClickClosable: false },
          });
          // 打开抽屉
          archInfo.openDrawer();
        }}>
          {t('确定')}
        </Button>
      </Modal.Footer>
    </Modal>
  </div>;
};

export default forwardRef(ResourceList);
