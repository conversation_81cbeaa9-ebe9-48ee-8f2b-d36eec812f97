/* eslint-disable */
import React, { useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import originStore from '@src/origin-store/store';
import { t } from '@tea/app/i18n';
import {
  setGuardInfo,
} from '@src/utils/caching';

import moment from 'moment';
import EscortDrawer from '@src/pages/escortDrawer/index';
import { EENDTYPE, guardStatusEnum } from '@src/constants';
import { getStatu, dataReport, queryStringObj, processState, getSearchParam } from '@src/utils/common';
import { changeCommonData, useCommonSelector, resetCommonData } from '@src/store/app-common';
import GuardTopPanel from '../guard-Top-Panel';
import {
  setDrawerVisibleMapAction,
  setCurrentNodeAction,
  setAgentDrawerVisible,
} from '@src/origin-store/guardAction';
import ResourceList from '@src/components/resourceList/index';
import {
  DescribeOtherPlatformGuardSheet,
  GetAccountInfoByFields,
  CreateGuardSheet,
  DescribeGuardApplyAuth,
} from '@src/service/api/baseInfo';
import {
  saveArchDiagram,
} from '@src/service/api/broadcast-agent-chat';
import { map, pick } from 'lodash';

const MainContent = () => {
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');
  const ChildRef = useRef(null);

  const firstUpdateFlag = useRef(true);
  const firstAfterSaleFlagDrawer = useRef(true);
  const firstOpenUrlDrawer = useRef(true);
  const firstOpenInfoUpdateDrawer = useRef(true);
  const { guardStatus, guardInfoDetail, taskResult, isUserAuth }  = useCommonSelector();
  const {
    graphApi,
    drawerUpdateFlag,
  }  = originStore.getState().guard;
  const isISA = graphApi.env === 'ISA';

  const dispatch = useDispatch();
  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[graphApi.env];

  const handleStartTask = () => {
    ChildRef?.current?.startTask();
  };

  // 更新护航状态
  const updateGuardStatus = (guardInfo) => {
    let guardStatus = guardStatusEnum.NO_GUARD_DRAFT;
    if (guardInfo?.Status) {
      // 护航开始、结束时间
      const { StartTime, EndTime } = guardInfo;
      const today = moment();
      const start = moment(StartTime, 'YYYY-MM-DD HH:mm:ss');
      const end = moment(EndTime, 'YYYY-MM-DD HH:mm:ss');
      if (today.isAfter(end)) {
        // 过期
        guardStatus = guardStatusEnum.NO_GUARD_DRAFT;
      } else if (guardInfo.Status === 1) {
        // 未发起护航有草稿
        guardStatus = guardStatusEnum.GUARD_DRAFT;
      } else {
        guardStatus = today.isBefore(start) ? guardStatusEnum.GUARD_BEFORE_PARPARE : guardStatusEnum.GUARDING;
      }
    } else {
      // 未发起护航且无草稿
      guardStatus = guardStatusEnum.NO_GUARD_DRAFT;
    }
    dispatch(changeCommonData({
      guardStatus,
    }));
  };

  const fetchUserAuth = () => {
    DescribeGuardApplyAuth()
      .then((res: any) => {
        dispatch(changeCommonData({
          isUserAuth: res?.IsGuardApply,
        }));
        // 无权限更新节点后抽屉
        if (!res?.IsGuardApply) {
          handleStartTask();
          nodeMonitorOpenDrawer();
          dataReport(graphApi);
        }
      });
  };

  // 创建护航单
  const createGuardSheet = () => {
    if (!guardInfoDetail.GuardId) {
      GetAccountInfoByFields()
        .then((res: any) => {
          const appIdInfo = res || {};
          const params: any = {
            GuardId: guardInfoDetail?.GuardId || 0,
            CustomerName: appIdInfo?.CustomerName?.trim() || '',
            // 草稿
            Status: 1,
            Platform: graphApi.env,
            PlatformUniqueId: graphApi.archInfo?.archId,
            GuardName: t('{{CustomerName}}护航需求{{CurrentTime}}', { CustomerName: appIdInfo.CustomerName, CurrentTime: moment().format('YYYYMMDDHH') }),
            StartTime: moment().add(2, 'hours')
              .startOf('hour')
              .format('YYYY-MM-DD HH:mm:ss'),
            EndTime: moment().add(1, 'days')
              .startOf('hour')
              .format('YYYY-MM-DD HH:mm:ss'),
            Standard: appIdInfo.HasAfterSale ? 0 : 1,
          };
          if (type === EENDTYPE.OPERATOR) {
            params.MainAppId = appId;
          }
          CreateGuardSheet(params).then((res: any) => {
            if (res.Id) {
              DescribeOtherPlatformGuardSheet({
                Filters: [{
                  Name: 'platform_unique_id',
                  Values: [graphApi.archInfo?.archId],
                }] })
                .then((res: any) => {
                  // 最后一条护航单
                  const guard = res.Guard?.length ? res.Guard[0] : {};
                  const guardInfo = getStatu(guard) === 'finish' ? {} : guard;

                  // 如果最后一条已经护航完成，则不缓存
                  setGuardInfo(guardInfo);
                  dispatch(changeCommonData({
                    guardInfoDetail: guardInfo,
                  }));
                  ChildRef?.current?.getNodeInfo();
                  updateGuardStatus(guardInfo);
                  dataReport(graphApi, guardInfo.GuardId);
                });
            }
          });
        })
        .catch((err) => {
          console.log(err);
        });
    }
  };

  // 获取护航单信息
  const fetchGuardInfo = () => {
    DescribeOtherPlatformGuardSheet({
      Filters: [{
        Name: 'platform_unique_id',
        Values: [graphApi?.archInfo?.archId],
      }],
    })
      .then((res: any) => {
        const guard = res.Guard?.length ? res.Guard[0] : {};
        // 如果最后一条已经护航完成，则不缓存
        const guardInfo = getStatu(guard) === 'finish' ? {} : guard;

        dispatch(changeCommonData({
          guardInfoDetail: guardInfo,
        }));
        setGuardInfo(guardInfo);
        // 更新护航状态
        updateGuardStatus(guardInfo);
        ChildRef?.current?.getNodeInfo();

        if (firstOpenInfoUpdateDrawer.current) {
        // 首次才打开信息修改抽屉
          const queryObj: any = queryStringObj(location?.search);
          if (queryObj?.fromOperate === 'infoUpdate' && isISA) {
            // 信息变更抽屉
            originStore.dispatch(setDrawerVisibleMapAction({ infoChangeDrawerVisible: true }));
            firstOpenInfoUpdateDrawer.current = false;
          }
        }

        // 首次更新数据上报
        if (firstUpdateFlag.current) {
          handleStartTask();
          dataReport(graphApi, guardInfo.GuardId);
          firstUpdateFlag.current = false;
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  // 节点监控打开抽屉
  const nodeMonitorOpenDrawer = () => {
    const queryObj: any = queryStringObj(location?.search);
    if (queryObj?.fromOperate === 'nodeMonitoring' && queryObj.nodeUuid) {
      // 如果有节点，就打开节点监控抽屉
      const node = graphApi?.getArchNodes()?.[queryObj.nodeUuid] || '';
      if (node && isISA) {
        originStore.dispatch(setCurrentNodeAction(node));
        graphApi.setShapeChecked([node.key], true);
        originStore.dispatch(setDrawerVisibleMapAction({ nodeDrawerVisible: true }));
      } else {
        originStore.dispatch(setCurrentNodeAction(node));
        graphApi.setShapeChecked([node.key], true);
        originStore.dispatch(setDrawerVisibleMapAction({ nodeCustomerDrawerVisible: true }));
      }
    }
  };

  const saveArchDiagramList = () => {
    if (isISA) return;
    const nodeList = graphApi.getNodeList();
    const list: any = map(nodeList, item => ({
      ...pick(item, ['DiagramId', 'NodeName', 'ProductType', 'BindingType', 'ParentDiagramId']),
    }));
    saveArchDiagram({
      ArchId: graphApi.archInfo.archId,
      DiagramNodeList: list,
    })
      .then((res: any) => {
        console.log(res);
        const queryObj: any = queryStringObj(location?.search);
        if (queryObj?.fromOperate || queryObj?.fromPlatform) return;
        originStore.dispatch(setAgentDrawerVisible(true));
      })
      .catch((err: any) => {
        console.log(err);
      });
  };

  // 外链打开抽屉统一处理
  const urlOpenDrawer = ()  => {
    if (!firstOpenUrlDrawer.current) return;
    const queryObj: any = queryStringObj(location?.search);
    // 安兔外链打开抽屉统一处理
    if (queryObj?.fromPlatform === 'Antool') {
      // 设置发起护航抽屉内容
      graphApi.setDrawerProps({
        title: t('发起护航'),
        children: <EscortDrawer />,
        className: 'escortDrawerWrap',
        extra: { outerClickClosable: false },
      });
      graphApi.openDrawer();
      firstOpenUrlDrawer.current = false;
    }
    /**
     * fromOperate
     * jump 点击架构图
     * copy 点击复制
     * infoUpdate 点击护航信息变更
     * instanceUpdate 点击护航实例变更
     * nodeMonitoring 点击节点监控
     * broadcastedit 点击播报编辑
     */
    // 获取url全部查询参数
    if (queryObj.fromOperate === 'jump') {
      // 打开发起护航抽屉
      if (guardStatus === guardStatusEnum.GUARDING || guardStatus === guardStatusEnum.GUARD_BEFORE_PARPARE) {
        graphApi.setDrawerProps({
          title: guardStatus === guardStatusEnum.GUARDING ? t('护航中') : t('护航前准备'),
          children: <EscortDrawer />,
          className: 'escortDrawerWrap',
          extra: { outerClickClosable: false },
        });
        graphApi.openDrawer();
        setTimeout(() => {
          // 关闭历史记录抽屉
          originStore.dispatch(setDrawerVisibleMapAction({ historyDrawerVisible: false }));
        }, 100);
        firstOpenUrlDrawer.current = false;
      } else {
        // 打开历史记录抽屉
        originStore.dispatch(setDrawerVisibleMapAction({ historyDrawerVisible: true }));
        firstOpenUrlDrawer.current = false;
      }
    } else if (queryObj.fromOperate === 'copy') {
      if (guardStatus !== guardStatusEnum.GUARDING && guardStatus !== guardStatusEnum.GUARD_BEFORE_PARPARE) {
        // 打开发起护航抽屉
        graphApi.setDrawerProps({
          title: t('发起护航'),
          children: <EscortDrawer copyId={Number(queryObj.guardId)} />,
          className: 'escortDrawerWrap',
          extra: { outerClickClosable: false },
        });
        graphApi.openDrawer();
        firstOpenUrlDrawer.current = false;
      }
    } else if (queryObj.fromOperate === 'instanceUpdate') {
      // 打开节点设置抽屉
      graphApi.setDrawerProps({
        title: t('节点设置'),
        children: <ResourceList canEdit={true} />,
        className: isISA ? 'nodeSettingWrap wrapPlus' : 'nodeSettingWrap',
        extra: { outerClickClosable: false },
      });
      graphApi.openDrawer();
      firstOpenUrlDrawer.current = false;
    } else if (queryObj.fromOperate === 'broadcastedit') {
      // 打开播报编辑抽屉
      if (guardInfoDetail.Status >= 32 && guardInfoDetail?.IsConfirm === 1) {
        originStore.dispatch(setDrawerVisibleMapAction({ broadcastVisible: true }));
        firstOpenUrlDrawer.current = false;
      }
    }
  };

  useEffect(() => {
    dispatch(changeCommonData({ appId }));
    if (isISA) {
      dispatch(changeCommonData({
        isUserAuth: true,
      }));
    } else {
      fetchUserAuth();
    }
    saveArchDiagramList();
    graphApi.setProgressParams(0, t('配置更新中'), {
      hoverText: '0%',
    });

    return () => {
      dispatch(resetCommonData());
    };
  }, []);

  useEffect(() => {
    // 其他抽屉需要异步更新完后打开
    if (taskResult?.ProgressValue === 1) {
      const {
        IsConfirm: afterSaleIsConfirm,
        IsNeedConfirm,
      } = guardInfoDetail.Approvals?.AfterSaleConfirmStatus || {};
      // 从url中获取 fromOperate
      const fromOperate = getSearchParam('fromOperate', location);
      // eslint-disable-next-line max-len
      if (IsNeedConfirm && !afterSaleIsConfirm && isISA && guardInfoDetail.Status > processState.onSaleApprovalId && !fromOperate) {
        if (firstAfterSaleFlagDrawer.current) {
          originStore.dispatch(setDrawerVisibleMapAction({ cloudResourceVisible: true }));
          firstAfterSaleFlagDrawer.current = false;
        }
      }
    }
  }, [guardInfoDetail, taskResult?.ProgressValue]);

  useEffect(() => {
    // 节点监控异步状态也可以打开抽屉
    setTimeout(() => {
      nodeMonitorOpenDrawer();
    }, 0);
  }, []);

  useEffect(() => {
    if (taskResult?.ProgressValue === 1) {
      // 其他抽屉需要异步更新完后打开
      urlOpenDrawer();
    }
  }, [taskResult?.ProgressValue, guardStatus, guardInfoDetail]);

  useEffect(() => {
    if (isUserAuth) {
      fetchGuardInfo();
    }
  }, [isUserAuth, drawerUpdateFlag]);

  useEffect(() => {
    if (taskResult?.ProgressValue === 1) {
      if (isUserAuth) {
        createGuardSheet();
      } else {
        ChildRef?.current?.getNodeInfo();
      }
    }
  }, [isUserAuth, taskResult?.ProgressValue, guardInfoDetail]);

  return <GuardTopPanel onRef={ChildRef}/>;
};
export default MainContent;
