/* eslint-disable no-param-reassign */

import React, { useEffect, useMemo } from 'react';
import { reportGuard } from '@src/utils/caching';
import PlaySvgIcon from '@src/assets/svg/play.svg';
import SettingSvgIcon from '@src/assets/svg/setting.svg';
import InfoIconSvg from '@src/assets/svg/info.svg';
import DownloadSvgIcon from '@src/assets/svg/download.svg';
import {
  setBroadcastLinkIdAction,
  setDrawerVisibleMapAction,
  setAgentDrawerVisible,
} from '@src/origin-store/guardAction';
import store from '@src/origin-store/store';
import PendingSvgIcon from '@src/assets/svg/pending.svg';
import ReadySvgIcon from '@src/assets/svg/preparation-icon.svg';
import { t } from '@tea/app/i18n';
import { guardStatusEnum } from '@src/constants';
import { AILogo } from '@tencent/cloud-chat-ui';
import EscortDrawer from '@src/pages/escortDrawer/index';
import ResourceList from '@src/components/resourceList/index';
import OtherProduct from '@src/pages/otherProduct/index';
import { useCommonSelector } from '@src/store/app-common';
import './index.less';

export const CustomerGuardStatusMap = {
  [`${guardStatusEnum.GUARD_DRAFT}`]: {
    iconNode: <img src={PlaySvgIcon} alt={t('发起护航')} />,
    desc: t('发起护航'),
    disabled: false,
    hoverText: t('发起护航'),
  },
  [`${guardStatusEnum.GUARDING}`]: {
    iconNode: <img src={PendingSvgIcon} alt={t('护航中')} />,
    desc: t('护航中'),
    disabled: false,
    hoverText: t('护航中'),
    color: '#0ABF5B',
  },
  [`${guardStatusEnum.GUARD_BEFORE_PARPARE}`]: {
    iconNode: <img src={ReadySvgIcon} alt={t('需求处理中')} />,
    desc: t('需求处理中'),
    disabled: false,
    hoverText: t('需求处理中'),
    color: '#FF7200',
  },
  [`${guardStatusEnum.NO_GUARD_DRAFT}`]: {
    iconNode: <img src={PlaySvgIcon} alt={t('发起护航')} />,
    desc: t('发起护航'),
    disabled: true,
    hoverText: t('护航服务目前为腾讯云服务计划企业版/旗舰版客户专用'),
  },
};

export const CustomerGuardToolMap = {
  [`${guardStatusEnum.GUARD_DRAFT}`]: ['guard', 'broadcast-agent'],
  [`${guardStatusEnum.GUARDING}`]: ['guard', 'broadcast', 'broadcast-agent'],
  [`${guardStatusEnum.GUARD_BEFORE_PARPARE}`]: ['guard', 'broadcast-agent'],
  [`${guardStatusEnum.NO_GUARD_DRAFT}`]: ['guard', 'broadcast-agent'],
};

interface Iprops {
  uuid?: string;
  fromPlatform?:  string;
}

// 底部工具栏
const CustomerDuckMenu = ({}: Iprops) => {
  const { guardStatus, guardInfoDetail, asyncTaskId, taskResult }  = useCommonSelector();
  const {
    graphApi,
  }  = store.getState().guard;

  // 工具栏操作项
  const defaultOptions = useMemo(() => [
    {
      key: 'guard',
      ...CustomerGuardStatusMap[guardStatus],
      onClick: () => {
        // 发起护航上报
        if (guardStatus === guardStatusEnum.GUARD_DRAFT) {
          reportGuard(0);
        }
        // 统一打开抽屉逻辑
        store.dispatch(setDrawerVisibleMapAction({}));
        store.dispatch(setAgentDrawerVisible(false));
        graphApi.setDrawerProps({
          title: CustomerGuardStatusMap[guardStatus].desc,
          children: <EscortDrawer />,
          className: 'escortDrawerWrap',
          extra: { outerClickClosable: false },
        });
        graphApi.openDrawer();
      },
    },
    {
      key: 'broadcast',
      iconNode: <img src={SettingSvgIcon} alt={t('策略')} />,
      desc: t('护航播报'),
      disabled: true,
      hoverText: t('护航负责人审批与播报群创建后启用'),
      onClick: () => {
        store.dispatch(setAgentDrawerVisible(false));
        graphApi.closeDrawer();
        store.dispatch(setDrawerVisibleMapAction({ broadcastVisible: true }));
      },
    },
    {
      key: 'node-setting',
      iconNode: <img src={SettingSvgIcon} alt={t('设置')} />,
      desc: t('节点设置'),
      hoverText: t('节点设置'),
      disabled: false,
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({}));
        store.dispatch(setAgentDrawerVisible(false));
        graphApi.setDrawerProps({
          title: t('节点设置'),
          children: <ResourceList canEdit={true} />,
          className: 'nodeSettingWrap',
          extra: { outerClickClosable: false },
        });
        graphApi.openDrawer();
      },
    },
    {
      key: 'other-product',
      iconNode: <img src={InfoIconSvg} alt={t('其他产品')} />,
      desc: t('其他产品'),
      hoverText: t('其他产品'),
      disabled: true,
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({}));
        store.dispatch(setAgentDrawerVisible(false));
        graphApi.setDrawerProps({
          title: t('其他产品'),
          children: <OtherProduct />,
          className: 'otherProductWrap',
          extra: { outerClickClosable: false },
        });
        graphApi.openDrawer();
      },
    },
    {
      key: 'history',
      iconNode: <img src={DownloadSvgIcon} />,
      desc: t('历史护航'),
      hoverText: t('历史护航'),
      disabled: false,
      onClick: () => {
        store.dispatch(setAgentDrawerVisible(false));
        graphApi.closeDrawer();
        store.dispatch(setDrawerVisibleMapAction({ historyDrawerVisible: true }));
      },
    },
    {
      key: 'guard-item-adjust',
      ...CustomerGuardStatusMap[guardStatusEnum.GUARD_BEFORE_PARPARE],
      onClick: () => {
        // 统一打开抽屉逻辑
        store.dispatch(setDrawerVisibleMapAction({}));
        store.dispatch(setAgentDrawerVisible(false));
        graphApi.setDrawerProps({
          title: CustomerGuardStatusMap[guardStatusEnum.GUARD_BEFORE_PARPARE].desc,
          children: <EscortDrawer />,
          className: 'escortDrawerWrap',
          extra: { outerClickClosable: false },
        });
        graphApi.openDrawer();
      },
    },
    {
      key: 'broadcast-agent',
      iconNode: <AILogo loading={false} />,
      desc: t('播报订阅'),
      hoverText: t('播报订阅'),
      disabled: false,
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({}));
        graphApi.closeDrawer();
        store.dispatch(setAgentDrawerVisible(true));
      },
    },
  ], [guardStatus, guardInfoDetail]);

  const updateDuckTools = () => {
    const options = defaultOptions?.filter(item => CustomerGuardToolMap[guardStatus]?.includes(item.key)) as any;
    if (asyncTaskId) {
      options.forEach((item) => {
        item.disabled = true;
      });
    } else {
      switch (guardStatus) {
        case guardStatusEnum.GUARD_DRAFT:
          options.forEach((item) => {
            item.disabled = false;
          });
          // 未发起护航有草稿
          // 如果有客户接口人，说明用户已经点击过提交护航单了
          if ((guardInfoDetail.CloudGuardBaseInfoOtherPlatform || [])
            .find(i => i.PlatformUniqueId === graphApi.archInfo?.archId)?.Status === 2) {
            options[0] = defaultOptions[5];
            // options[1].disabled = true;
          }
          break;
        case guardStatusEnum.GUARDING:
          // 护航中
          options.forEach((item) => {
            if (item.key === 'broadcast') {
              if (guardInfoDetail.Status >= 32 && guardInfoDetail?.IsConfirm === 1) {
                // 护航负责人审批后才启用播报策略
                item.disabled = false;
              } else {
                item.disabled = true;
              }
            } else item.disabled = false;
          });
          break;
        case guardStatusEnum.GUARD_BEFORE_PARPARE:
          // 护航前准备
          options.forEach((item) => {
            item.disabled = false;
          });
          break;
        default:
          // 0可发起过护航，无草稿，不可以操作，可操作agent
          options.forEach((item) => {
            if (item.key === 'broadcast-agent') {
              item.disabled = false;
            } else item.disabled = true;
          });
      }
    }
    graphApi.setOperations(options);
    graphApi.showTools();
  };

  useEffect(() => {
    updateDuckTools();
  }, [guardStatus, guardInfoDetail, asyncTaskId]);
  useEffect(() => {
    if (taskResult?.ProgressValue === 1) {
      const { search } = location;
      const broadcastLinkId = search?.split('broadcastId')[1]?.split('=')[1];
      if (broadcastLinkId) {
        store.dispatch(setAgentDrawerVisible(true));
        store.dispatch(setBroadcastLinkIdAction(broadcastLinkId));
        const currentUrl = new URL(location.href);
        currentUrl.searchParams.delete('broadcastId');
        window.history.replaceState(null, '', currentUrl.toString());
      }
    }
  }, [taskResult]);

  return <></>;
};
export default CustomerDuckMenu;
