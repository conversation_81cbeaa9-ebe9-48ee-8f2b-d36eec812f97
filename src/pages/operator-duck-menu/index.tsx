/* eslint-disable no-param-reassign */
import React, { useEffect, useMemo } from 'react';
import { reportGuard } from '@src/utils/caching';
import PlaySvgIcon from '@src/assets/svg/play.svg';
import SettingSvgIcon from '@src/assets/svg/setting.svg';
import InfoIconSvg from '@src/assets/svg/info.svg';
import DownloadSvgIcon from '@src/assets/svg/download.svg';
import { setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
import store from '@src/origin-store/store';
import PendingSvgIcon from '@src/assets/svg/pending.svg';
import ReadySvgIcon from '@src/assets/svg/preparation-icon.svg';
import { t } from '@tea/app/i18n';
import EscortDrawer from '@src/pages/escortDrawer/index';
import ResourceList from '@src/components/resourceList/index';
import OtherProduct from '@src/pages/otherProduct/index';
import { guardStatusEnum } from '@src/constants';
import { useCommonSelector } from '@src/store/app-common';
import { AILogo } from '@tencent/cloud-chat-ui';
import './index.less';
import {  processState } from '@src/utils/common';

interface Iprops {
  uuid?: string;
  fromPlatform?:  string;
}

export const OperatorGuardStatusMap = {
  [`${guardStatusEnum.GUARD_DRAFT}`]: {
    iconNode: <img src={PlaySvgIcon} alt={t('发起护航')} />,
    desc: t('发起护航'),
    disabled: false,
    hoverText: t('发起护航'),
  },
  [`${guardStatusEnum.GUARDING}`]: {
    iconNode: <img src={PendingSvgIcon} alt={t('护航中')} />,
    desc: t('护航中'),
    disabled: false,
    hoverText: t('护航中'),
    color: '#0ABF5B',
  },
  [`${guardStatusEnum.GUARD_BEFORE_PARPARE}`]: {
    iconNode: <img src={ReadySvgIcon} alt={t('护航前准备')} />,
    desc: t('护航前准备'),
    disabled: false,
    hoverText: t('护航前准备'),
    color: '#FF7200',
  },
  [`${guardStatusEnum.NO_GUARD_DRAFT}`]: {
    iconNode: <img src={PlaySvgIcon} alt={t('发起护航')} />,
    desc: t('发起护航'),
    disabled: true,
    hoverText: t('护航服务目前为腾讯云服务计划企业版/旗舰版客户专用'),
  },
};

export const OperatorGuardToolMap = {
  [`${guardStatusEnum.GUARD_DRAFT}`]: ['guard', 'broadcast', 'node-setting', 'other-product', 'history'],
  [`${guardStatusEnum.GUARDING}`]: ['guard', 'broadcast', 'node-setting', 'history'],
  [`${guardStatusEnum.GUARD_BEFORE_PARPARE}`]: ['guard', 'broadcast', 'node-setting', 'history'],
  [`${guardStatusEnum.NO_GUARD_DRAFT}`]: ['guard', 'broadcast', 'node-setting', 'other-product', 'history'],
};

// 运营端底部工具栏
const OperatorDuckMenu = ({}: Iprops) => {
  const { guardStatus, guardInfoDetail, asyncTaskId }  = useCommonSelector();
  const {
    graphApi,
  }  = store.getState().guard;

  const hoverText = useMemo(() => <>
    <p>{t('- 什么时候修改？护航期间，在草稿或计算以外的状态')}</p>
    <p>{t('- 谁能修改？建单人、负责人、审批人')}</p>
    <p>{t('{{attr0}}', { attr0: getInstanceEditor().join(',') })}</p>
  </>, [guardInfoDetail]);

  // 工具栏操作项
  const defaultOptions = useMemo(() => [
    {
      key: 'guard',
      ...OperatorGuardStatusMap[guardStatus],
      onClick: () => {
        // 护航前准备售后确认架构抽屉弹框逻辑
        if (guardStatus === guardStatusEnum.GUARD_BEFORE_PARPARE) {
          const {
            IsConfirm: afterSaleIsConfirm,
            IsNeedConfirm,
          } = guardInfoDetail.Approvals?.AfterSaleConfirmStatus || {};
          if (IsNeedConfirm && !afterSaleIsConfirm && guardInfoDetail.Status > processState.onSaleApprovalId) {
            store.dispatch(setDrawerVisibleMapAction({ cloudResourceVisible: true }));
            return;
          }
        }
        // 发起护航上报
        if (guardStatus === guardStatusEnum.GUARD_DRAFT) {
          reportGuard(0);
        }
        // 统一打开抽屉逻辑
        store.dispatch(setDrawerVisibleMapAction({}));
        graphApi.setDrawerProps({
          title: OperatorGuardStatusMap[guardStatus].desc,
          children: <EscortDrawer />,
          className: 'escortDrawerWrap',
          extra: { outerClickClosable: false },
        });
        graphApi.openDrawer();
      },
    },
    {
      key: 'broadcast',
      iconNode: <img src={SettingSvgIcon} alt={t('策略')} />,
      desc: t('播报策略'),
      disabled: true,
      hoverText: t('护航负责人审批与播报群创建后启用'),
      onClick: () => {
        graphApi.closeDrawer();
        store.dispatch(setDrawerVisibleMapAction({ broadcastVisible: true }));
      },
    },
    {
      key: 'node-setting',
      iconNode: <img src={SettingSvgIcon} alt={t('设置')} />,
      desc: t('节点设置'),
      hoverText: t('节点设置'),
      disabled: false,
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({}));
        graphApi.setDrawerProps({
          title: t('节点设置'),
          children: <ResourceList canEdit={true} />,
          className: 'nodeSettingWrap wrapPlus',
          extra: { outerClickClosable: false },
        });
        graphApi.openDrawer();
      },
    },
    {
      key: 'other-product',
      iconNode: <img src={InfoIconSvg} alt={t('其他产品')} />,
      desc: t('其他产品'),
      hoverText: t('其他产品'),
      disabled: true,
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({}));
        graphApi.setDrawerProps({
          title: t('其他产品'),
          children: <OtherProduct />,
          className: 'otherProductWrap',
          extra: { outerClickClosable: false },
        });
        graphApi.openDrawer();
      },
    },
    {
      key: 'history',
      iconNode: <img src={DownloadSvgIcon} />,
      desc: t('历史护航'),
      hoverText: t('历史护航'),
      disabled: false,
      onClick: () => {
        graphApi.closeDrawer();
        store.dispatch(setDrawerVisibleMapAction({ historyDrawerVisible: true }));
      },
    },
    {
      key: 'broadcast-agent',
      iconNode: <AILogo loading={false} />,
      desc: t('播报订阅'),
      hoverText: t('播报订阅'),
      disabled: false,
      onClick: () => {
        graphApi.closeDrawer();
        store.dispatch(setDrawerVisibleMapAction({ agentDrawerVisible: true }));
      },
    },
  ], [guardStatus, guardInfoDetail]);

  const updateDuckTools = () => {
    const options = defaultOptions?.filter(item => OperatorGuardToolMap[guardStatus]?.includes(item.key)) as any;
    if (asyncTaskId) {
      options.forEach((item) => {
        item.disabled = true;
      });
    } else {
      switch (guardStatus) {
        case guardStatusEnum.GUARD_DRAFT:
          // 未发起护航有草稿
          options.forEach((item) => {
            if (item.key === 'broadcast') {
              item.disabled = true;
            } else item.disabled = false;
          });
          break;
        case guardStatusEnum.GUARDING:
          // 护航中
          options.forEach((item) => {
            if (item.key === 'node-setting') {
              const hasInstanceModAuth = isInstanceModifyAllowed();
              item.disabled = !hasInstanceModAuth;
              item.hoverText = !hasInstanceModAuth ? hoverText : item.hoverText;
            } else if (item.key === 'broadcast') {
              if (guardInfoDetail.Status >= 32 && guardInfoDetail?.IsConfirm === 1) {
                // 护航负责人审批后才启用播报策略
                item.disabled = false;
              } else {
                item.disabled = true;
              }
            } else item.disabled = false;
          });
          break;
        case guardStatusEnum.GUARD_BEFORE_PARPARE:
          // 护航前准备
          options.forEach((item) => {
            if (item.key === 'node-setting') {
              const hasInstanceModAuth = isInstanceModifyAllowed();
              item.disabled = !hasInstanceModAuth;
              item.hoverText = !hasInstanceModAuth ? hoverText : item.hoverText;
            } else if (item.key === 'broadcast') {
              if (guardInfoDetail.Status >= 32 && guardInfoDetail?.IsConfirm === 1) {
                // 护航负责人审批后才启用播报策略
                item.disabled = false;
              } else {
                item.disabled = true;
              }
            } else item.disabled = false;
          });
          break;
        default:
          // 0可发起过护航，无草稿，不可以操作
          options.forEach((item) => {
            item.disabled = true;
          });
      }
    }
    graphApi.setOperations(options);
    graphApi.showTools();
  };

  // 查询是有修改护航单实例的人员：建单人、改单人、APPID负责人、审批人及售后指派人
  function getInstanceEditor() {
    let guys = [];
    guys = guys.concat(guardInfoDetail?.Approvals?.AfterSalesStatus?.Handler?.split(';'))
      .concat(guardInfoDetail?.Approvals?.AfterSalesStatus?.Supporter?.split(';'));
    if (guardInfoDetail?.Approvals?.ExpertStatus) {
      guardInfoDetail.Approvals.ExpertStatus.map((i) => {
        guys = guys.concat(i.Handler.split(';'));
      });
    }
    if (guardInfoDetail?.Approvals?.ScanResultStatus) {
      guardInfoDetail.Approvals.ScanResultStatus.map((i) => {
        guys = guys.concat(i.Handler.split(';'));
      });
    }
    guys = guys.concat(guardInfoDetail?.CreatedBy?.trim())
      .concat(guardInfoDetail?.UpdatedBy?.trim())
      .concat(guardInfoDetail?.Responser?.split(';'));
    return Array.from(new Set(guys)).filter(i => i !== '');
  }

  // 查询是否有修改护航单实例权限
  function isInstanceModifyAllowed() {
    let stateAllowed = true;
    let guysAllowed = true;
    // 护航期间，草稿状态和计算状态不允许修改
    if (!guardInfoDetail.GuardInfoSupportUpdate || guardInfoDetail.Status <= processState.onNoteId
            || [processState.onRunningId, processState.instanceAltering, processState.instanceAlteredRun]
              .includes(guardInfoDetail.Status)) {
      stateAllowed = false;
    }
    // 护航负责人审批状态，允许修改。该状态较特殊
    if (processState.onRunningId === guardInfoDetail.Status && !guardInfoDetail.Approvals.AfterSalesStatus.IsConfirm) {
      stateAllowed = true;
    }
    // 有权限修改的人员
    const insEditorList = getInstanceEditor();
    if (insEditorList.indexOf(graphApi?.userName) === -1) {
      guysAllowed = false;
    }
    return stateAllowed && guysAllowed;
  }

  useEffect(() => {
    updateDuckTools();
  }, [guardStatus, guardInfoDetail, asyncTaskId]);

  return <></>;
};
export default OperatorDuckMenu;
