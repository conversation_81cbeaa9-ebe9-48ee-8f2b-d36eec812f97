import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { ChatTabs, ChatInstructionList } from '@tencent/cloud-chat-ui';
import { Bubble, StatusTip, message } from '@tencent/tea-component';
import { describeGuardAISubscribeMetricInfo } from '@src/service/api/broadcast-agent-chat';
import { useMetricSelector, changeMetricData } from '@src/store/metric-data';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';

interface BroadcastListProps {
  onClose?: () => void;
  textareaRef: any;
  visible?: boolean;
}

const BroadcastList: React.FC<BroadcastListProps> = ({
  onClose,
  textareaRef,
  visible,
}) => {
  const dispatch = useDispatch();
  const { contentTabs, tabsDataMap } = useMetricSelector();
  const {
    agentInputValue,
    isTemplateEdit,
    editMessageId,
    editTemplateContent,
  } = useCommonSelector();
  const [activeId, setActiveId] = useState('');
  const [loading, setLoading] = useState(false);

  const fetchMetricData = async () => {
    setLoading(true);
    try {
      const res: any = await describeGuardAISubscribeMetricInfo();
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        setLoading(false);
        return;
      }

      const temTabs: any[] = [];
      const temMap: any = {};
      const temList: any[] = [];

      res?.ProductMetricInfoList?.forEach((item: any) => {
        temTabs.push({
          id: item.Product,
          label: item.ProductName,
        });
        temMap[item.Product] = item.MetricInfoList;
        item.MetricInfoList?.forEach((el: any) => {
          temList.push({
            id: item.Product,
            productName: item.ProductName,
            metricInfo: el,
          });
        });
      });

      dispatch(changeMetricData({
        contentTabs: temTabs,
        tabsDataMap: temMap,
        list: temList,
      }));

      setLoading(false);

      if (temTabs.length > 0) {
        setActiveId(temTabs[0].id);
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      message.error({ content: errorMsg });
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && contentTabs.length === 0) {
      fetchMetricData();
    }
  }, [visible]);

  useEffect(() => {
    if (contentTabs.length > 0 && !activeId) {
      setActiveId(contentTabs[0].id);
    }
  }, [contentTabs]);

  if (loading) {
    return (
      <div className="status-wrap">
        <StatusTip status="loading" />
      </div>
    );
  }

  return (
    <>
      {
        contentTabs?.length > 0 ? (
          <ChatTabs
            placement='left'
            tabs={contentTabs}
            activeId={activeId}
            tabBarRender={
              (children, tab) => (
                  <Bubble className={'sub-config-tab-l-bubble'} content={tab.label ?? ''} openDelay={700}>
                    <a
                      className={`tea-tabs__tab ${tab.id === activeId ? 'is-active' : ''}`}
                      onClick={
                        () => {
                          setActiveId(tab.id);
                        }
                      }
                    >
                      {children}
                    </a>
                  </Bubble>
              )
            }
          >
            {
              contentTabs?.map((item, i) => (
                <ChatTabs.TabPanel id={item.id} key={i}>
                  <ChatInstructionList>
                    {tabsDataMap?.[item.id]?.map((el: any, index: number) => (
                      <Bubble content={el.MetricName ?? ''} key={index} openDelay={700}>
                        <ChatInstructionList.Item
                          label={el.MetricName}
                          key={index}
                          showCollect={false}
                          onClick={
                            () => {
                              const messageVal = `${item.id} ${el.MetricName}`;
                              if (isTemplateEdit && editMessageId) {
                                const newContent = `${editTemplateContent || ''}${editTemplateContent ? '\n' : ''}${messageVal}`;
                                dispatch(changeCommonData({ editTemplateContent: newContent }));
                              } else {
                                const inputMessageVal = `${agentInputValue}${agentInputValue && '\n'}${messageVal}`;
                                // 正常状态：设置到ChatSender
                                dispatch(changeCommonData({ agentInputValue: inputMessageVal }));
                                setTimeout(() => {
                                  textareaRef?.current?.focus();
                                  const length = textareaRef?.current?.value.length; // 获取文本长度
                                  textareaRef?.current?.setSelectionRange(length, length);
                                  textareaRef.current.scrollTop = textareaRef.current?.scrollHeight || 0;
                                }, 0);
                              }
                              onClose?.();
                            }
                          }
                        />
                      </Bubble>
                    ))}
                  </ChatInstructionList>
                </ChatTabs.TabPanel>
              ))
            }
          </ChatTabs>
        ) : (
          <div className="status-wrap">
            <StatusTip status={'empty'} />
          </div>
        )
      }
    </>
  );
};

export default BroadcastList;
