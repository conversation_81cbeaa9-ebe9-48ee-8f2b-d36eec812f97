import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { StatusTip, message } from '@tencent/tea-component';
import { describeSubscribeHistoryTemplateInfo } from '@src/service/api/broadcast-agent-chat';
import { useMetricSelector, changeMetricData } from '@src/store/metric-data';
import originStore from '@src/origin-store/store';
import { useCommonSelector } from '@src/store/app-common';
import { StatusEunm } from '@src/pages/guard-agent-chat/contants';
import { t } from '@tea/app/i18n';
import './index.less';

interface MySubscribeListProps {
  onClose?: () => void;
  visible?: boolean;
  onSendMessage: (message: string, intention?: string) => void;
}

const MySubscribeList: React.FC<MySubscribeListProps> = ({
  onClose,
  visible,
  onSendMessage,
}) => {
  const dispatch = useDispatch();
  const { mySubscribeList } = useMetricSelector();
  const {
    isTemplateEdit,
  }  = useCommonSelector();
  const [loading, setLoading] = useState(false);
  const { graphApi: apis }  = originStore.getState().guard;
  const fetchSubscribeList = async () => {
    setLoading(true);
    try {
      const res: any = await describeSubscribeHistoryTemplateInfo({
        ArchId: apis?.archInfo?.archId,
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        setLoading(false);
        return;
      }

      dispatch(changeMetricData({
        mySubscribeList: res?.HistoryTemplateInfoList,
      }));

      setLoading(false);
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      message.error({ content: errorMsg });
      setLoading(false);
    }
  };

  const handleItemClick = (item: any) => {
    if (item.Status === StatusEunm.offline || isTemplateEdit) return;
    const message = t('当前播报');
    onSendMessage?.(message, 'TemplateQueryNode');
    onClose?.();
  };

  useEffect(() => {
    if (visible) {
      fetchSubscribeList();
    }
  }, [visible]);

  if (loading) {
    return (
      <div className="status-wrap">
        <StatusTip status="loading" />
      </div>
    );
  }

  return (
    <div className='subscribe-list'>
      {
        mySubscribeList?.length > 0
          ? <>
              {
                mySubscribeList?.map((item: any) => (
                  <div
                    className={`subscribe-item ${(item.Status === StatusEunm.online && !isTemplateEdit) ? 'status-online' : 'status-offline'}`}
                    key={item.TemplateId}
                    onClick={() => handleItemClick(item)}
                  >
                    <div className="subscribe-item-name">
                      <span
                        className='subscribe-item-name-text'
                        title={item.TemplateName}
                      >
                        {item.TemplateName}
                      </span>
                      [
                        <>
                          {
                            item.Status === StatusEunm.online
                              ? <span className="subscribe-item-status-online">{t('已启用')}</span>
                              : <span className="subscribe-item-status-offline">{t('已停用')}</span>
                          }
                        </>
                      ]
                    </div>
                    <div className="subscribe-item-time">
                      {item.UpdateTime}
                    </div>
                  </div>
                ))
              }
          </>
          : <div className="status-wrap">
            <StatusTip status={'empty'} />
          </div>
      }
    </div>
  );
};

export default MySubscribeList;
