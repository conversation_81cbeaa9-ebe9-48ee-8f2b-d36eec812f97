/**
 * @description GuardAgentChat抽屉
 */
import React, { useEffect, useState, useRef } from 'react';
import {
  Bubble, Button, message as tip,
} from '@tencent/tea-component';
import { app } from '@tea/app';
import { ContentType, MessageType, MessageTypeEnum } from '@src/types/ai-chat';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { getPopupContainer } from '@src/utils/common';
import originStore from '@src/origin-store/store';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import { useDispatch } from 'react-redux';
import { ClearIcon } from '@tencent/tea-icons-react';
import {
  setBroadcastLinkIdAction,
  setAgentDrawerVisible,
} from '@src/origin-store/guardAction';
import { SCOPE_SUBSCRIBE_NODE, SCOPE_QUERY_NODE, SCOPE_DISABLE_NODE } from '@src/pages/guard-agent-chat/contants';
import { InstructionStarIcon, LogIcon, ConsultIcon, ResourceIcon } from '@tencent/cloud-chat-ui/dist/kits/icons';
import { v4 as uuidv4 } from 'uuid';
import { isEmpty, find, map, filter, sortBy, get } from 'lodash';
import {
  getGuideQuestion,
} from '@src/service/api/broadcast-agent-chat';
import {
  useChatStore,
  Chat,
  ChatOnboarding,
  Stack,
  ChatSuggestionList,
  ChatSender,
} from '@tencent/cloud-chat-ui';
import MessageItem from './components/message-item';
import { t, Trans } from '@tea/app/i18n';
import SubscribeConfigBotDrawer from '@src/pages/guard-agent-chat/components/subscribe-config-bot-drawer';
import BroadcastRecordDrawer from '@src/pages/guard-agent-chat/components/broadcast-record-drawer';
import './index.less';

interface IProps {
  visible: boolean;
}

enum DisplayType {
  BottomTip = 0,
  InputTip = 1,
  InitType = 2
}

const IconMap = {
  0: <ConsultIcon />,
  1: <ResourceIcon />,
  2: <ResourceIcon />,
};

let timer;

const GuardAgentChat = ({ visible }: IProps) => {
  const { graphApi: apis, broadcastLinkId }  = originStore.getState().guard;
  const { isTemplateEdit, agentInputValue } = useCommonSelector();
  const dispatch = useDispatch();

  const [messages, setMessages] = useState<MessageType[]>([]);
  const [initQuickList, setInitQuickList] = useState([]);
  const [suggestionList, setSuggestionList] = useState([]);
  const { processing, setProcessing } = useChatStore();
  const cancelRef = useRef(null);
  const [sessionId, setSessionId] = useState<string>(uuidv4());
  const scrollerRef = useRef({});

  const chatIdRef = useRef('');
  const [subDrawerVisible, setSubDrawerVisible] = useState(false);
  const [broadcastRecordVisible, setBroadcastRecordVisible] = useState(false);
  const [broadcastId, setBroadcastId] = useState('');
  const textareaRef = useRef<any>(null);

  // 处理sse异常
  const handleError = (str?: string, ctrl?: any) => {
    setProcessing(false);
    setMessages((prev) => {
      const lastContent = prev[prev.length - 1]?.content;
      return [...(prev.slice(0, prev.length - 1)), {
        type: MessageTypeEnum.assistant,
        content: `${lastContent}
${(str || t('查询失败,请稍后再试'))}
`,
        contentType: ContentType.text,
        id: chatIdRef.current,
      }];
    });
    ctrl?.abort?.();
  };

  // 发送消息前对messages预处理
  const handleInitSendMessage = (v: string, options?: {
    addLast?: boolean; // 是否追加
    error?: string; // 报错信息
  }) => {
    const { addLast = false } = options || {};
    setSuggestionList([]);
    // 正常消息
    if (!addLast) {
      setMessages(prev => [...prev, {
        type: MessageTypeEnum.user,
        content: v,
        contentType: ContentType.text,
        id: uuidv4(),
      }, {
        id: uuidv4(),
        type: MessageTypeEnum.assistant,
        content: '',
        contentType: ContentType.text,
        deepThink: '',
      }]);
    } else {
      setMessages(prev =>
        // 整理最后一条消息
        [...(prev.slice(0, prev.length - 1)), {
          type: MessageTypeEnum.assistant,
          content: prev[prev.length - 1]?.content,
          contentType: ContentType.text,
          id: prev[prev.length - 1].id,
        }]);
    }
  };
  const handleNodeListShine = (nodeList) => {
    apis?.removeAllNodeClass?.();
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    if (nodeList) {
      apis?.addNodeClass(nodeList, 'shine-node');
      timer = setTimeout(() => {
        apis?.removeAllNodeClass?.();
        apis?.addNodeClass(nodeList, 'color-node');
      }, 3000);
    }
  };
  const handleEventStep = (data, id, ctrl) => {
    try {
      const {
        StepKey: stepKey,
        StepData: result,
      } = data;

      if ((stepKey === SCOPE_QUERY_NODE || stepKey === SCOPE_SUBSCRIBE_NODE) && !isEmpty(result)) {
        handleNodeListShine(result);
      }
      if (stepKey === SCOPE_DISABLE_NODE) {
        apis?.removeAllNodeClass?.();
      }
      setMessages((prev) => {
        const stepItem = find(prev[prev.length - 1]?.steps, item => item.stepKey === stepKey) || {};
        if (isEmpty(stepItem)) {
          const stepList = prev[prev.length - 1]?.steps || [];
          stepList.push({
            stepKey,
            stepContent: result,
          });
          return [...(prev.slice(0, prev.length - 1)), {
            id,
            type: MessageTypeEnum.assistant,
            contentType: ContentType.text,
            steps: stepList,
            deepThink: prev[prev.length - 1]?.deepThink,
            content: prev[prev.length - 1]?.content,
            costTime: prev[prev.length - 1]?.costTime,
          }];
        }
        const newSteps = prev[prev.length - 1]?.steps?.map((item) => {
          if (item.stepKey === stepKey) {
            return {
              stepKey,
              stepContent: result,
            };
          }
          return item;
        });
        return [...(prev.slice(0, prev.length - 1)), {
          id,
          type: MessageTypeEnum.assistant,
          contentType: ContentType.text,
          steps: newSteps,
          deepThink: prev[prev.length - 1]?.deepThink,
          content: prev[prev.length - 1]?.content,
          costTime: prev[prev.length - 1]?.costTime,
        }];
      });
    } catch (e) {
      handleError(e.message, ctrl);
      console.error('json error', e);
    }
  };

  const handleEventThought = (data, id) => {
    setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
      id,
      type: MessageTypeEnum.assistant,
      contentType: ContentType.text,
      steps: prev[prev.length - 1]?.steps,
      deepThink: (prev[prev.length - 1]?.deepThink ?? '') + (data ?? ''),
      costTime: prev[prev.length - 1]?.costTime ?? 0,
      content: prev[prev.length - 1]?.content,
    }]);
  };

  const handleEventEnd = (data, ctrl) => {
    setProcessing(false);
    ctrl.abort();
    if (data?.CurrentOperate) {
      fetchQuestionList({ action: data.CurrentOperate });
    } else {
      setSuggestionList([]);
    }
  };

  // 处理接收到的消息
  const handleReciveMessage = (params: {
    rsp: {data: string; event?: string};
    ctrl: any;
  }) => {
    const { rsp, ctrl } = params;
    setProcessing(true);
    if (!rsp?.data) {
      console.log(t('收到心跳:'), rsp);
      return;
    }

    try {
      const { Data: rsData, Event: rsEvent, Id: id, Response } = JSON.parse(rsp?.data);
      if (get(Response, 'Error.Code') === 'AuthFailure.UnauthorizedOperation') {
        handleError(get(Response, 'Error.Message'), ctrl);
        return;
      }
      switch (rsEvent) {
        case 'Step':
          handleEventStep(rsData, id, ctrl);
          break;
        case 'Thought':
          handleEventThought(rsData, id);
          break;
        case 'Message':
          setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
            id,
            type: MessageTypeEnum.assistant,
            contentType: ContentType.text,
            steps: prev[prev.length - 1]?.steps,
            deepThink: prev[prev.length - 1]?.deepThink,
            content: (prev[prev.length - 1]?.content ?? '') + (rsData ?? ''),
            costTime: prev[prev.length - 1]?.costTime,
          }]);
          break;
        case 'End':
          handleEventEnd(rsData, ctrl);
          break;
        default:
          console.warn('Unknown event type:');
      }
    } catch (e) {
      handleError(e.message, ctrl);
      console.error('json error', e);
    }
  };

  // 处理发送消核心逻辑
  const handleSendMessage = (
    v: string,
    intention?: string, // 意图场景
    options?: {
      addLast?: boolean; // 是否追加
      error?: string; // 报错信息
    }
  ) => {
    if (processing || !v) return;
    setProcessing(true);
    const { addLast = false } = options || {};
    dispatch(changeCommonData({ agentInputValue: '' }));
    handleInitSendMessage(v, options);
    // 控制取消请求
    const ctrl = new AbortController();
    cancelRef.current = ctrl;
    const isConsole = apis?.env === 'CONSOLE';
    let url;
    let method;
    let headers;
    let body;
    const question = v;
    if (isConsole) {
      const info = app.capi.createSSERequest({
        serviceType: 'saai',
        cmd: 'GuardAgentCompletions',
        data: {
          Version: '2025-03-17',
          ArchId: apis?.archInfo?.archId,
          Message: question,
          SessionId: sessionId,
          ...(intention && { Intention: intention }),
        },
        regionId: 1,
      });
      const {
        url: u, method: m, headers: h, body: b,
      } = info;
      url = u;
      method = m;
      headers = h;
      body = b;
    } else {
      // 运营端SSE请求
      // const params = {
      //   AppId: appId,
      //   User: apis?.userName,
      //   SessionId: sessionId,
      //   Message: question,
      // };

      // url = 'http://11.141.10.186/template/stream';
      // method = 'post';
      // headers = {
      //   'Content-Type': 'application/json',
      // };
      // body = JSON.stringify(params);
    }

    fetchEventSource(url, {
      method,
      headers,
      body,
      credentials: 'include',
      openWhenHidden: true,
      signal: ctrl.signal,
      // 必须设置，否则出现异常无法终止
      onopen(res): any {
        if (res.status !== 200) {
          handleError(t('网络异常'), ctrl);
        } else if (!addLast) {
          setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
            type: MessageTypeEnum.assistant,
            content: '',
            contentType: ContentType.text,
            id: prev[prev.length - 1].id,
          }]);
        }
      },
      onmessage(rsp) {
        handleReciveMessage({
          rsp,
          ctrl,
        });
      },
      onerror(e) {
        handleError(e.message, ctrl);
        console.error('sse error', e, ctrl);
      },
    });
  };

  const fetchQuestionList = ({ action }) => {
    getGuideQuestion({
      ArchId: apis?.archInfo?.archId,
      CurrentOperate: action,
    })
      .then((res: any) => {
        const initList = filter(res?.GuideQuestionList, item => item.Display === DisplayType.InitType);
        if (!isEmpty(initList)) {
          const list = map(sortBy(initList, ['SortIndex']), (item) => {
            const question = item?.Question?.split(':');
            return {
              title: `${question[0]}${question[1] ? ':' : ''}`,
              icon: IconMap[item.Category],
              description: question[1],
              onClick: () => {
                handleSendMessage(item.Question, item?.Intention);
                setSuggestionList([]);
              },
            };
          });
          setInitQuickList(list);
        }
        const bottomTipList = filter(res?.GuideQuestionList, item => item.Display === DisplayType.BottomTip);
        if (!isEmpty(bottomTipList)) {
          const list = map(sortBy(bottomTipList, ['SortIndex']), item => ({
            tag: t('操作'),
            text: item.Question,
            onClick: () => {
              handleSendMessage(item.Question, item?.Intention);
            },
          }));
          setSuggestionList(list);
        } else {
          setSuggestionList([]);
        }
        const inputTipList = filter(res?.GuideQuestionList, item => item.Display === DisplayType.InputTip);
        if (!isEmpty(inputTipList)) {
          let question = '';
          map(sortBy(inputTipList, ['SortIndex']), (item) => {
            question +=  item.Question;
          });
          dispatch(changeCommonData({ agentInputValue: question }));
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const stopMessage = () => {
    if (!processing) return;
    cancelRef.current?.abort();
    setProcessing(false);
  };

  const handleSessionClear = () => {
    apis?.removeAllNodeClass?.();
    setSessionId(uuidv4());
    setMessages([]);
  };

  useEffect(() => {
    if (visible && messages.length === 0) {
      fetchQuestionList({ action: 'AccessAgent' });
    }
  }, [visible]);

  useEffect(() => {
    if (visible && broadcastLinkId) {
      setBroadcastId(broadcastLinkId);
      setBroadcastRecordVisible(true);
      setTimeout(() => {
        originStore.dispatch(setBroadcastLinkIdAction(''));
      }, 0);
    }
  }, [visible, broadcastLinkId]);

  return <div>
    <Chat
      title={
        messages.length > 0
          ? <div className='chat-title-drawer'>
            <Trans>
              云护航 AI 助手
              <div className='chat-action-tag'>
                Beta
              </div>
            </Trans>
          </div>
          : ''
      }
      visible={visible}
      isInSessions={messages.length > 0}
      fixedTop={50}
      className='chat-drawer-container'
      portalContainer={getPopupContainer()}
      bounds={{ top: 100, left: 100, right: 10, bottom: 30 }}
      logo={null}
      onCloseButtonClick={() => {
        stopMessage();
        originStore.dispatch(setAgentDrawerVisible(false));
        setSubDrawerVisible(false);
        setBroadcastRecordVisible(false);
        apis?.removeAllNodeClass?.();
        setBroadcastId('');
      }}
      inputArea={
        <>
          <ChatSender
            getTextareaRef={(node) => {
              textareaRef.current = node;
            }}
            value={agentInputValue}
            loading={processing}
            disabled={isTemplateEdit}
            topExtra={
              <>
                <Button
                  onClick={() => {
                    setSubDrawerVisible(true);
                  }}
                >
                  <Trans>
                    <InstructionStarIcon />
                    订阅配置
                  </Trans>
                </Button>
                <Button
                  onClick={() => {
                    setBroadcastRecordVisible(true);
                  }}
                >
                  <Trans>
                    <LogIcon />
                    播报记录
                  </Trans>
                </Button>
              </>
            }
            onChange={(v) => {
              dispatch(changeCommonData({ agentInputValue: v }));
            }}
            onSend={(v) => {
              if (v.trim().length <= 0) {
                return tip.warning({
                  content: t('请勿使用空白信息进行询问'),
                });
              }
              if (v.trim().length > 1024) {
                return tip.warning({
                  content: t('消息长度不能超过1024个字符'),
                });
              }
              handleSendMessage(v);
            }}
            onCancelProcess={stopMessage}
          />
        </>
      }
      actions={[
        ...(messages.length === 0 ? [
          <div key="beta-tag" className='chat-action-tag'>
            Beta
          </div>,
        ] : []),
        <Bubble
          arrowPointAtCenter
          placement='bottom'
          className='chat-ui__bubble'
          openDelay={100}
          content={t('清空会话')}
          key={uuidv4()}
        >
          <Button type='text' onClick={handleSessionClear}>
            <ClearIcon />
          </Button>
        </Bubble>,
      ]}
      scrollerRef={scrollerRef}
    >
      {
        messages.length > 0
          ? <Stack>
            {
              messages.map((item, index) => (
                <MessageItem
                  key={index}
                  item={item}
                  processing={processing}
                  messages={messages}
                  onSendMessage={(message, intention) => {
                    handleSendMessage(message, intention);
                  }}
                  onMessageChange={value => setMessages(value)}
                />
              ))
            }
            {!processing && messages.length > 0 && (
              <ChatSuggestionList
                list={suggestionList}
              />
            )}
          </Stack>
          : <ChatOnboarding
            subTitle={t('我是云护航Agent')}
            description={t('在这里我可以帮你查询并订阅你所关注的业务指标与状态播报')}
            quickList={initQuickList}
          />
      }
      <BroadcastRecordDrawer
        visible={broadcastRecordVisible}
        broadcastId={broadcastId}
        archId={apis?.archInfo?.archId}
        onClose={
          () => {
            setBroadcastRecordVisible(false);
          }
        }
      />
      <SubscribeConfigBotDrawer
        visible={subDrawerVisible}
        textareaRef={textareaRef}
        onClose={() => {
          setSubDrawerVisible(false);
        }}
        onSendMessage={(message, intention) => {
          setSubDrawerVisible(false);
          handleSendMessage(message, intention);
        }}
      />
    </Chat>
  </div>;
};
export default GuardAgentChat;
