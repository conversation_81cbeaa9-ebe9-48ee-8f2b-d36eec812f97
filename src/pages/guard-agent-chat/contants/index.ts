
export const TEMPLATE_MOCK = 'TemplateRenderNode';

export const TEMPLATE_QUERY_MOCK = 'TemplateQueryNode';

export const SCOPE_QUERY_NODE = 'ScopeQueryNode';

export const SCOPE_SUBSCRIBE_NODE = 'ScopeSubscribeNode';

export const SCOPE_DISABLE_NODE = 'ScopeDisableNode';

export enum TitleTabsEnum {
  template = 'template',
  mySubscribe = 'mySubscribe',
}


export enum StatusEunm {
  online = 1,
  offline = 0,
}
