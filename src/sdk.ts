/**
 * @fileoverview CLOUD-ESCORT SDK 入口文件
 */
import './i18n';
// 导入依赖
import { app } from '@tencent/tea-app';
import '@tencent/cloud-chat-ui/dist/assets/style.css';
import '@tencent/tea-component/dist/tea.css';
import 'tdesign-react/es/style/index.css';
import './global.css';
import './style.css';
import store from './origin-store/store';

import {
  setDrawerVisibleMapAction,
  setCurrentNodeAction,
  setAgentDrawerVisible,
} from '@src/origin-store/guardAction';

import Init from '@src/Init';

// 注册 SDK 入口，提供 SDK 工厂方法
app.sdk.register('cloud-escort-sdk', () =>
  // 返回 SDK 对外暴露的 API
  ({
    /**
     * SDK 测通方法，可保留可不保留
     */
    hello() {
      return 'Hello from CLOUD-ESCORT SDK';
    },
    init: Init,
    destroy: () => {
      store.dispatch(setDrawerVisibleMapAction({}));
      store.dispatch(setAgentDrawerVisible(false));
      store.dispatch(setCurrentNodeAction({}));
    },
  }));
