export enum MessageTypeEnum {
  user = 'user',
  assistant = 'assistant',
}
export enum ContentType {
  text = 'text',
  image = 'image',
  iframeUrl = 'iframeUrl',
  chart = 'chart',
  default = 'default'
}

export interface MessageType {
  id?: string;
  type: MessageTypeEnum,
  content: any,
  contentType: ContentType;
  questions?: string[];
  deepThink?: string;
  requestId?: string;
  steps?: any;
  costTime?: number;
}
