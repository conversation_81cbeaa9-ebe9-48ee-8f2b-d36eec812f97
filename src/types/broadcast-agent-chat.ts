export interface Threshold {
  Values: number
  Factor: string
  Unit: string
}

export interface SaveBroadcastTemplateParams {
  ArchId: string,
  BroadcastCycle: number,
  StartTime?: string,
  EndTime?: string,
  TemplateAnalysisData: string;
  BroadcastChannel: any;
  ChatGroupId: string;
  WebHook?: string;
}

export interface DescribeGuideQuestionParmas {
  CurrentOperate: string;
  ArchId: string;
}

export interface SaveArchDiagramParams {
  DiagramNodeList: {
    DiagramId: string;
    NodeName: string;
    ProductType: string;
    BindingType: string;
    ParentDiagramId: string;
  }[];
  ArchId: string;
}

export interface DescribeGuardSubscribeChannelParams {
  GuardId: number;
  MockAppid?: number;
  MockUin?: string;
}

export interface DescribeBroadcastMessageHistoryParams {
  ArchId: string;
  Title: string;
  BroadcastMessageId: string;
  Offset: number;
  Limit: number;

  OrderByTime: boolean
}

export interface DisableBroadcastParams {
  ArchId: string;
}
