import request from '../request';
import {
  SaveBroadcastTemplateParams,
  DescribeGuideQuestionParmas,
  SaveArchDiagramParams,
  DescribeGuardSubscribeChannelParams,
  DescribeBroadcastMessageHistoryParams,
  DisableBroadcastParams,
} from '@src/types/broadcast-agent-chat';

/**
 * @description 护航播报-查询播报订阅内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const getGuideQuestion = (data: DescribeGuideQuestionParmas) => request('DescribeGuideQuestion', data);

/**
 * @description 护航播报-保存架构图节点
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const saveArchDiagram = (data: SaveArchDiagramParams) => request('SaveArchDiagram', data);

/**
 * @description 护航播报-播报渠道
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const getGuardSubscribeChannel = (data: DescribeGuardSubscribeChannelParams) => request('DescribeGuardSubscribeChannel', data);

/**
 * @description 护航播报-启用播报
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const broadcastSubscribe = () => request('BroadcastSubscribe', {});

/**
 * @description 获取AI订阅指标指标信息
 * <AUTHOR>
 * @link
  * @param data
 * @returns Promise<unknown>
 */
export const describeGuardAISubscribeMetricInfo = () => request('DescribeGuardAISubscribeMetricInfo', {}, '', { tipLoading: false });


/**
 * @description 获取AI订阅我的订阅信息
 * <AUTHOR>
 * @link
  * @param data
 * @returns Promise<unknown>
 */
export const describeSubscribeHistoryTemplateInfo = (data: {ArchId: string}) => request('DescribeSubscribeHistoryTemplateInfo', data, '', { tipLoading: false });

/**
 * @description 查询播报历史记录
 * <AUTHOR>
 * @link
  * @param data
 * @returns Promise<unknown>
 */
export const describeBroadcastMessageHistory = (data: DescribeBroadcastMessageHistoryParams) => request('DescribeBroadcastMessageHistory', data, '', { tipLoading: false });

/**
 * @description 护航播报-启用播报
 * <AUTHOR>
 * @link
  * @param data
 * @returns Promise<unknown>
 */
export const saveBroadcastTemplate = (data: SaveBroadcastTemplateParams) => request('SaveBroadcastTemplate', data);

/**
 * @description 护航播报-停用播报
 * <AUTHOR>
 * @link
  * @param data
 * @returns Promise<unknown>
 */
export const disableBroadcast = (data: DisableBroadcastParams) => request('DisableBroadcast', data);
