import moment from 'moment';
import {
  SET_CURRENT_NODE,
  SET_DRAWER_VISIBLE_MAP,
  SET_NODE_CUSTOMER_DRAWER_RANGE_TIME,
  SET_GRAPH_DATA,
  RESET_STORE,
  SET_NODE_CUSTOMER_DRAWER_METRICS,
  SET_NODE_CUSTOMER_DRAWER_CURRENT_METRICS,
  SET_HISTORY_GUARD,
  SET_GUARD_UPDATE_FLAG,
  SET_BROADCAST_LINK_ID,
  SET_AGENT_DRAWER_VISIBLE,
} from './constants';

// 初始值
const defaultState = {
  currNode: {},
  nodeCustomerDrawerRangeTime: [moment().startOf('day')
    .toDate(), moment().endOf('day')
    .toDate()], // 租户端抽屉 时间范围
  nodeFullScreenModalVisible: false,
  metricsFetchThrottle: 4, // 租户端抽屉 请求并发数量
  drawerMetrics: [], // 租户端抽屉 待查询的指标集
  curDrawerMetrics: [], // 租户端抽屉 当前要查询的指标集
  graphApi: {},
  // 选择的历史护航数据
  historyGuard: {},
  // 抽屉可见性
  drawerVisibleMap: {
    nodeDrawerVisible: false,
    // 租户端 点击节点抽屉
    nodeCustomerDrawerVisible: false,
    // 播报抽屉
    broadcastVisible: false,
    // 售后架构图资源确认抽屉
    cloudResourceVisible: false,
    // 历史护航抽屉标志
    historyDrawerVisible: false,
    // 基本信息变更抽屉标志
    infoChangeDrawerVisible: false,
  },
  drawerUpdateFlag: false, // 护航信息是否需要刷新标志
  broadcastLinkId: '',
  // AI agent 对话抽屉
  agentDrawerVisible: false,
};

// eslint-disable-next-line import/prefer-default-export, default-param-last
export const guardReducer = (preState = defaultState, action) => {
  switch (action.type) {
    case RESET_STORE:
      return { ...defaultState };
    case SET_CURRENT_NODE:
      return { ...preState, currNode: action.payload };
    case SET_DRAWER_VISIBLE_MAP:
      return { ...preState, drawerVisibleMap: {
        ...Object.fromEntries(Object.entries(preState.drawerVisibleMap).map(([key]) => [key, false])),
        ...action.payload,
      } };
    case SET_NODE_CUSTOMER_DRAWER_RANGE_TIME:
      return { ...preState, nodeCustomerDrawerRangeTime: action.payload };
    case SET_NODE_CUSTOMER_DRAWER_METRICS:
      return { ...preState, drawerMetrics: action.payload };
    case SET_NODE_CUSTOMER_DRAWER_CURRENT_METRICS:
      return { ...preState, curDrawerMetrics: action.payload };
    case SET_GRAPH_DATA:
      return { ...preState, graphApi: action.payload };
    case SET_HISTORY_GUARD:
      return { ...preState, historyGuard: action.payload };
    case SET_GUARD_UPDATE_FLAG:
      return { ...preState, drawerUpdateFlag: action.payload };
    case SET_BROADCAST_LINK_ID:
      return { ...preState, broadcastLinkId: action.payload };
    case SET_AGENT_DRAWER_VISIBLE:
      return { ...preState, agentDrawerVisible: action.payload };
    default:
      return preState;
  }
};
