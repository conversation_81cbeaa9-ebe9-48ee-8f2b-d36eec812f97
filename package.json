{"name": "@tencent/tea-sdk-cloud-escort", "version": "1.0.0", "description": "The cloud-escort tea sdk for qcloud console", "main": "src/app.js", "scripts": {"dev": "CI=1 tea dev", "scan": "tea scan", "build": "tea build", "deploy": "tea commit", "build-types": "rm -rf dts/types && npx tsc -d --emitDeclarationOnly --skipLibCheck --declarationDir dts/types", "publish-types": "cd dts && npm version patch && tnpm publish"}, "keywords": ["tea", "sdk", "cloud-escort"], "engines": {"typescript": ">3.3"}, "license": "UNLICENSED", "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@reduxjs/toolkit": "^2.2.7", "@tencent/cloud-chat-ui": "0.7.8", "@tencent/qmfe-yoa-react-ui": "^1.0.25", "@tencent/tea-app": "^2.0.0", "@tencent/tea-component": "2.7.9", "@tencent/tea-icons-react": "^1.0.58", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "6.2.0", "axios": "^1.7.2", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "eslint": "^8.57.0", "lodash": "^4.17.21", "moment": "^2.30.1", "nanoid": "^5.0.7", "react": "^16.8.3", "react-dom": "^16.8.3", "react-hook-form": "7.54.2", "react-redux": "^8.0.5", "react-syntax-highlighter": "^15.6.1", "redux": "^5.0.1", "swr": "^2.2.5", "tdesign-react": "^1.8.1"}, "devDependencies": {"@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "@hot-loader/react-dom": "^16.11.0", "@svgr/webpack": "^5.1.0", "@tencent/eslint-config-tencent": "^0.12.2", "@tencent/eslint-plugin-tea-i18n": "^0.1.1", "@tencent/tea-scripts": "^2.0.0", "@tencent/tea-types": "^0.1.0", "@types/react": "^16.8.4", "@types/react-dom": "^16.8.2", "@types/react-router-dom": "^4.3.1", "@types/webpack": "^4.4.32", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "6.2.0", "eslint": "^7.12.1", "eslint-config-airbnb": "^19.0.4", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.5", "eslint-webpack-plugin": "2.5.4", "react-uuid": "^2.0.0", "typescript": "^4.3.5"}}